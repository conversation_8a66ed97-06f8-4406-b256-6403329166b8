<?php
/**
 * Plugin Name: WP Bulk Category Uploader
 * Description: Upload multiple categories and subcategories via CSV file
 * Version: 1.0.0
 * Author: Your Name
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WPBulkCategoryUploader {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_post_upload_categories', array($this, 'handle_upload'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_admin_menu() {
        add_management_page(
            'Bulk Category Upload',
            'Category Upload',
            'manage_categories',
            'bulk-category-upload',
            array($this, 'admin_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'tools_page_bulk-category-upload') return;
        
        wp_enqueue_style('bulk-category-style', plugin_dir_url(__FILE__) . 'style.css');
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>Bulk Category Upload</h1>
            
            <?php if (isset($_GET['success'])): ?>
                <div class="notice notice-success">
                    <p>Categories uploaded successfully! <?php echo intval($_GET['count']); ?> categories processed.</p>
                </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['error'])): ?>
                <div class="notice notice-error">
                    <p>Error: <?php echo esc_html($_GET['error']); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="card">
                <h2>Upload CSV File</h2>
                <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="upload_categories">
                    <?php wp_nonce_field('upload_categories_nonce'); ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">CSV File</th>
                            <td>
                                <input type="file" name="category_csv" accept=".csv" required>
                                <p class="description">Upload a CSV file with categories</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Taxonomy</th>
                            <td>
                                <select name="taxonomy">
                                    <option value="category">Category</option>
                                    <option value="post_tag">Tags</option>
                                    <?php
                                    $taxonomies = get_taxonomies(array('public' => true), 'objects');
                                    foreach ($taxonomies as $taxonomy) {
                                        if (!in_array($taxonomy->name, ['category', 'post_tag'])) {
                                            echo '<option value="' . esc_attr($taxonomy->name) . '">' . esc_html($taxonomy->label) . '</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </td>
                        </tr>
                    </table>
                    
                    <?php submit_button('Upload Categories'); ?>
                </form>
            </div>
            
            <div class="card">
                <h2>CSV Format Instructions</h2>
                <p>Your CSV file should have the following format:</p>
                <pre>Parent Category,Child Category,Parent Description,Child Description
Clothing,Men,Men's clothing items
Clothing,Women,Women's clothing items
Electronics,Mobiles,Mobile phones and accessories
Electronics,,Electronics category only</pre>
                
                <h3>Sample CSV Download</h3>
                <a href="<?php echo plugin_dir_url(__FILE__) . 'sample.csv'; ?>" class="button">Download Sample CSV</a>
            </div>
        </div>
        <?php
    }
    
    public function handle_upload() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'upload_categories_nonce')) {
            wp_die('Security check failed');
        }
        
        if (!current_user_can('manage_categories')) {
            wp_die('Insufficient permissions');
        }
        
        if (!isset($_FILES['category_csv']) || $_FILES['category_csv']['error'] !== UPLOAD_ERR_OK) {
            wp_redirect(admin_url('tools.php?page=bulk-category-upload&error=upload_failed'));
            exit;
        }
        
        $taxonomy = sanitize_text_field($_POST['taxonomy']);
        $file = $_FILES['category_csv']['tmp_name'];
        
        $result = $this->process_csv($file, $taxonomy);
        
        if ($result['success']) {
            wp_redirect(admin_url('tools.php?page=bulk-category-upload&success=1&count=' . $result['count']));
        } else {
            wp_redirect(admin_url('tools.php?page=bulk-category-upload&error=' . urlencode($result['error'])));
        }
        exit;
    }
    
    private function process_csv($file, $taxonomy) {
        $handle = fopen($file, 'r');
        if (!$handle) {
            return array('success' => false, 'error' => 'Could not read CSV file');
        }
        
        $count = 0;
        $header = fgetcsv($handle); // Skip header row
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (empty($data[0])) continue;
            
            $parent_name = trim($data[0]);
            $child_name = isset($data[1]) ? trim($data[1]) : '';
            $parent_description = isset($data[2]) ? trim($data[2]) : '';
            $child_description = isset($data[3]) ? trim($data[3]) : '';
            
            // Create parent category
            $parent_term = $this->create_term($parent_name, $taxonomy, 0, $parent_description);
            if (is_wp_error($parent_term)) continue;
            
            $count++;
            
            // Create child category if specified
            if (!empty($child_name)) {
                $child_term = $this->create_term($child_name, $taxonomy, $parent_term['term_id'], $child_description);
                if (!is_wp_error($child_term)) {
                    $count++;
                }
            }
        }
        
        fclose($handle);
        
        return array('success' => true, 'count' => $count);
    }
    
    private function create_term($name, $taxonomy, $parent_id = 0, $description = '') {
        // Check if term already exists
        $existing = term_exists($name, $taxonomy, $parent_id);
        if ($existing) {
            return $existing;
        }
        
        $args = array(
            'description' => $description,
            'parent' => $parent_id
        );
        
        return wp_insert_term($name, $taxonomy, $args);
    }
}

// Initialize the plugin
new WPBulkCategoryUploader();
