=== WP Bulk Category Uploader ===
Contributors: yourname
Tags: categories, bulk upload, csv, import, taxonomy
Requires at least: 5.0
Tested up to: 6.4
Stable tag: 1.0.0
License: GPL v2 or later

Upload multiple WordPress categories and subcategories via CSV file.

== Description ==

WP Bulk Category Uploader allows you to efficiently upload multiple categories and subcategories to your WordPress site using a simple CSV file format.

Features:
* Upload categories via CSV
* Support for parent-child relationships
* Works with any custom taxonomy
* Include category descriptions
* Simple and intuitive interface

== Installation ==

1. Upload the plugin files to `/wp-content/plugins/wp-bulk-category-uploader/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to Tools > Category Upload to use the plugin

== CSV Format ==

Your CSV should have three columns:
- Parent Category (required)
- Child Category (optional)
- Description (optional)

Example:
```
Parent Category,Child Category,Description
Clothing,Men,Men's clothing items
Electronics,,Electronics category only
```

== Changelog ==

= 1.0.0 =
* Initial release